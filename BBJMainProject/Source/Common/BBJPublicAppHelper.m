//
//  BBJPublicAppHelper.m
//  IMYBabyJournal
//
//  Created by lxb on 2019/4/12.
//  Copyright © 2019 MeetYou. All rights reserved.
//

#import "BBJPublicAppHelper.h"
#import "BBJPublicFun.h"

@implementation IMYPublicAppHelper (BJPublicAppHelper)

IMY_KYLIN_FUNC_PREMAIN{
    kIMY_MeetyouAppId = @"25";
    kIMY_AppleStoreId = @"1459838978";
    kIMY_MeetyouAppName = @"美柚宝宝记";
    kIMY_MeetyouAppScheme = @"meetyou.linggan.seeyoubaby";
    [[IMYThemeManager sharedIMYThemeManager] addConfigFileName:@"BBJ_ThemeValue.json"];
}

+ (Class)usingAppHelperClass {
    return [BBJPublicAppHelper class];
}
@end

@implementation BBJPublicAppHelper


- (BOOL)isPersonalRecommand{
    return [[IMYUserDefaults standardUserDefaults] boolForKey:@"BBJOpenIndividuation"];
}

- (void)setIsPersonalRecommand:(BOOL)value{
    [[IMYUserDefaults standardUserDefaults] setBool:value forKey:@"BBJOpenIndividuation"];
}

////是否 同意 App的隐私权限申明，同意后才能注册第三方SDK
//@property (nonatomic, assign) BOOL hasAgreedPrivacy;

- (BOOL)hasAgreedPrivacy{
    return [[IMYUserDefaults standardUserDefaults] boolForKey:@"BBJ_hasAgreedPrivacy"];
}

- (void)setHasAgreedPrivacy:(BOOL)value{
    [[IMYUserDefaults standardUserDefaults] setBool:value forKey:@"BBJ_hasAgreedPrivacy"];
}

- (NSString *)userid {
    if ([BBJPublicFun getVirtualSuccess]) {
        return [BBJPublicFun getVirtualUserID];
    } else {
        return [BBJPublicFun userID];
    }
}

- (NSString *)virtualToken {
    return [BBJPublicFun getVirtualToken];
}

- (NSString *)userToken {
    return [BBJPublicFun userToken];
}

- (void)setUserToken:(NSString *)userToken {
    [BBJPublicFun setUserToken:userToken];
}

@end
