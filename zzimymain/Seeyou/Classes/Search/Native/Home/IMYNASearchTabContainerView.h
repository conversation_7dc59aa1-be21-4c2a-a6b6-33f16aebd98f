//
//  IMYNASearchTabContainerView.h
//  ZZIMYMain
//

#import <UIKit/UIKit.h>
#import "IMYNASearchHotspotsView.h"
#import "IMYNASearchHotDiscussionView.h"

@class IMYPageViewController;

NS_ASSUME_NONNULL_BEGIN

@interface IMYNASearchTabContainerView : UIView

@property (nonatomic, copy) NSString *name;

/// 初始化搜索热点数据
- (void)setupWithHotspotsKeyModels:(NSArray<IMYNASearchHotspotsKeyModel *> *)keyModels
                          animated:(BOOL)animated;

/// 初始化热门讨论数据
- (void)setupWithHotDiscussionKeyModels:(NSArray<IMYNASearchHotDiscussionKeyModel *> *)keyModels
                               animated:(BOOL)animated;

/// 获取当前搜索热点数据
@property (nonatomic, copy, readonly) NSArray<IMYNASearchHotspotsKeyModel *> *hotspotsKeys;

/// 获取当前热门讨论数据
@property (nonatomic, copy, readonly) NSArray<IMYNASearchHotDiscussionKeyModel *> *hotDiscussionKeys;

/// 高度变化
@property (nonatomic, copy) void(^onHeightDidChangedBlock)(BOOL anim);

/// 搜索热点词曝光
@property (nonatomic, copy) void(^onHotspotsKeyDidExposuredBlock)(IMYNASearchHotspotsKeyModel *keyModel);

/// 搜索热点词被点击
@property (nonatomic, copy) void(^onHotspotsKeyDidPressedBlock)(IMYNASearchHotspotsKeyModel *keyModel);

/// 热门讨论词曝光
@property (nonatomic, copy) void(^onHotDiscussionKeyDidExposuredBlock)(IMYNASearchHotDiscussionKeyModel *keyModel);

/// 热门讨论词被点击
@property (nonatomic, copy) void(^onHotDiscussionKeyDidPressedBlock)(IMYNASearchHotDiscussionKeyModel *keyModel);

/// 空白区域被点击
@property (nonatomic, copy) void(^onEmptyDidClickedBlock)(void);

/// 实验配置值（用于手势冲突处理）
@property (nonatomic, assign, readonly) NSInteger searchHotExperimentValue;

/// 页面控制器（用于获取当前tab索引）
@property (nonatomic, strong, readonly) IMYPageViewController *pageViewController;

/// 迭代8.95：动态控制tab显示状态
- (void)updateTabVisibilityWithHotDiscussionAvailable:(BOOL)hasHotDiscussion animated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END
