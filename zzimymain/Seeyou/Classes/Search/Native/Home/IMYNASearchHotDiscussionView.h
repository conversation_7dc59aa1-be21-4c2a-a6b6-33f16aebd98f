//
//  IMYNASearchHotDiscussionView.h
//  ZZIMYMain
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYNASearchHotDiscussionKeyModel : NSObject
/// 显示的文案
@property (nonatomic, copy) NSString *show_word;
/// 搜索的文案
@property (nonatomic, copy) NSString *query_word;
/// 图标类型：0无，1热，2荐，3新，4沸，5广告
@property (nonatomic, assign) NSInteger icon;
/// 其他暂时无用
@property (nonatomic, assign) NSInteger id;
@property (nonatomic, assign) NSInteger type;
@property (nonatomic, copy) NSString *scheme_uri;
@property (nonatomic, copy) NSString *click_ping;
@property (nonatomic, copy) NSString *show_ping;
@property (nonatomic, copy) NSString *key;
@property (nonatomic, copy) NSString *jump_address;

/// 本地字段当前model在数组第几位
@property (nonatomic, assign) NSInteger index;

/// 广告相关
@property (nonatomic, assign) BOOL ad_model;
@property (nonatomic, assign) BOOL ad_isUriPush;
@property (nonatomic, assign) NSInteger ad_index;
@property (nonatomic, copy) NSString *ad_icon;
@property (nonatomic, assign) NSInteger ad_icon_pos;

@end


@interface IMYNASearchHotDiscussionView : UIView

@property (nonatomic, copy) NSString *name;

/// 初始化
- (void)setupWithHotDiscussionKeyModels:(NSArray<IMYNASearchHotDiscussionKeyModel *> *)keyModels
                               animated:(BOOL)animated;
@property (nonatomic, copy, readonly) NSArray<IMYNASearchHotDiscussionKeyModel *> *hotDiscussionKeys;

/// 高度变化
@property (nonatomic, copy) void(^onHeightDidChangedBlock)(BOOL anim);

/// 搜索词曝光
@property (nonatomic, copy) void(^onKeyDidExposuredBlock)(IMYNASearchHotDiscussionKeyModel *keyModel);

/// 搜索词被点击
@property (nonatomic, copy) void(^onKeyDidPressedBlock)(IMYNASearchHotDiscussionKeyModel *keyModel);

/// 空白区域被点击
@property (nonatomic, copy) void(^onEmptyDidClickedBlock)(void);

@end

NS_ASSUME_NONNULL_END
