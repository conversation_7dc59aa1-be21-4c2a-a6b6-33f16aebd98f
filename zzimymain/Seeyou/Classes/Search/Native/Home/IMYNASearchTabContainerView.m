//
//  IMYNASearchTabContainerView.m
//  ZZIMYMain
//
//  Created by ljh on 2023/8/7.
//

#import "IMYNASearchTabContainerView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYPageViewController.h>

@interface IMYNASearchTabContainerView () <IMYPageViewControllerDataSource, IMYPageViewControllerDelegate>

@property (nonatomic, strong) UIView *topBar;
@property (nonatomic, strong) UILabel *titleLabel;

// Tab 相关
@property (nonatomic, strong) UIView *tabContainer;
@property (nonatomic, strong) UIButton *searchHotButton;
@property (nonatomic, strong) UIButton *hotDiscussionButton;
@property (nonatomic, strong) UIView *indicatorView;

// 子视图
@property (nonatomic, strong) IMYNASearchHotspotsView *hotspotsView;
@property (nonatomic, strong) IMYNASearchHotDiscussionView *hotDiscussionView;

// 实验配置
@property (nonatomic, assign, readwrite) NSInteger searchHotExperimentValue;

// 页面控制器
@property (nonatomic, strong, readwrite) IMYPageViewController *pageViewController;

@end

@implementation IMYNASearchTabContainerView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.imy_width = SCREEN_WIDTH;
    
    // 迭代8.95：获取hotlist实验配置
    // 值说明：0-未命中实验，1-实验组1（默认选中搜索热点），2-实验组2（默认选中热门讨论）
    IMYABTestExperiment *searchHotExp = [[IMYABTestManager sharedInstance] experimentForKey:@"hotlist"];
    self.searchHotExperimentValue = [searchHotExp.vars integerForKey:@"searchhot"];
    
    [self setupTopbar];
    [self setupPageViewController];
    self.name = @"搜索热点";
}

- (void)setName:(NSString *)name {
    if (!name.length) {
        return;
    }
    self.name = [name copy];
    if (self.searchHotExperimentValue == 0) {
        // 未命中实验，显示单一标题
        self.titleLabel.text = self.name;
    }
}

// 内部布局复杂，先不用autolayout了
- (void)setupTopbar {
    self.topBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 44)];
    [self addSubview:self.topBar];
    
    // 迭代8.95：根据实验值决定创建tab还是单一标题
    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        [self setupTabContainer];
    } else {
        [self setupSingleTitle];
    }
}

// 迭代8.95：创建单一标题（原有逻辑）
- (void)setupSingleTitle {
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 200, 44)];
    self.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [self.titleLabel imy_setTextColorForKey:kCK_Red_A];
    [self.topBar addSubview:self.titleLabel];
}

// 迭代8.95：创建tab容器
- (void)setupTabContainer {
    self.tabContainer = [[UIView alloc] initWithFrame:CGRectMake(12, 0, self.imy_width - 24, 44)];
    [self.topBar addSubview:self.tabContainer];

    // 创建并布局tab按钮（自适应宽度）
    [self createAndLayoutTabButtons];

    // 创建指示器
    [self createIndicator];

    // 根据实验值设置默认选中状态
    if (self.searchHotExperimentValue == 2) {
        [self selectHotDiscussionTab];
    } else {
        [self selectSearchHotTab];
    }
}

- (void)setupPageViewController {
    // 创建子视图
    [self setupChildViews];

    // 根据实验值决定是否创建页面控制器
    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        self.pageViewController = [[IMYPageViewController alloc] init];
        self.pageViewController.dataSource = self;
        self.pageViewController.delegate = self;
        self.pageViewController.cachePageCount = 2;

        // 添加到视图层级
        [self addSubview:self.pageViewController.view];
    } else {
        // 无tab模式，直接添加搜索热点视图
        [self addSubview:self.hotspotsView];
        self.hotspotsView.frame = CGRectMake(0, self.topBar.imy_bottom, self.imy_width, self.hotspotsView.imy_height);
    }
}

- (void)setupChildViews {
    // 创建搜索热点视图（恢复到原始状态，移除tab相关逻辑）
    self.hotspotsView = [IMYNASearchHotspotsView new];
    // 移除 titleLabel，因为容器会管理标题
    self.hotspotsView.name = nil;

    // 创建热门讨论视图
    self.hotDiscussionView = [IMYNASearchHotDiscussionView new];
    // 移除 titleLabel，因为容器会管理标题
    self.hotDiscussionView.name = nil;

    // 设置回调
    [self setupCallbacks];
}

- (void)setupCallbacks {
    @weakify(self);
    
    // 搜索热点回调
    self.hotspotsView.onHeightDidChangedBlock = ^(BOOL anim) {
        @strongify(self);
        [self updateContainerHeight];
    };

    self.hotspotsView.onKeyDidExposuredBlock = ^(IMYNASearchHotspotsKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotspotsKeyDidExposuredBlock) {
            self.onHotspotsKeyDidExposuredBlock(keyModel);
        }
    };

    self.hotspotsView.onKeyDidPressedBlock = ^(IMYNASearchHotspotsKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotspotsKeyDidPressedBlock) {
            self.onHotspotsKeyDidPressedBlock(keyModel);
        }
    };

    self.hotspotsView.onEmptyDidClickedBlock = ^{
        @strongify(self);
        if (self.onEmptyDidClickedBlock) {
            self.onEmptyDidClickedBlock();
        }
    };
    
    // 热门讨论回调
    self.hotDiscussionView.onHeightDidChangedBlock = ^(BOOL anim) {
        @strongify(self);
        [self updateContainerHeight];
    };

    self.hotDiscussionView.onKeyDidExposuredBlock = ^(IMYNASearchHotDiscussionKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotDiscussionKeyDidExposuredBlock) {
            self.onHotDiscussionKeyDidExposuredBlock(keyModel);
        }
    };

    self.hotDiscussionView.onKeyDidPressedBlock = ^(IMYNASearchHotDiscussionKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotDiscussionKeyDidPressedBlock) {
            self.onHotDiscussionKeyDidPressedBlock(keyModel);
        }
    };

    self.hotDiscussionView.onEmptyDidClickedBlock = ^{
        @strongify(self);
        if (self.onEmptyDidClickedBlock) {
            self.onEmptyDidClickedBlock();
        }
    };
}

#pragma mark - Tab Actions

- (void)onSearchHotButtonTapped {
    [self selectSearchHotTab];
    [self.pageViewController setViewControllerAtIndex:0 animated:YES];
}

- (void)onHotDiscussionButtonTapped {
    [self selectHotDiscussionTab];
    [self.pageViewController setViewControllerAtIndex:1 animated:YES];
}

- (void)selectSearchHotTab {
    // 设置颜色
    [self.searchHotButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];
    [self.hotDiscussionButton setTitleColor:[UIColor imy_colorForKey:kCK_Black_C] forState:UIControlStateNormal];

    // 设置字体大小：选中17，未选中16
    self.searchHotButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    self.hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];

    // 重新布局按钮（因为字体大小改变了）
    [self layoutTabButtonsWithAdaptiveWidth];

    // 移动指示器（自适应宽度）
    [UIView animateWithDuration:0.25 animations:^{
        [self updateIndicatorForButton:self.searchHotButton];
    }];
}

- (void)selectHotDiscussionTab {
    // 设置颜色
    [self.searchHotButton setTitleColor:[UIColor imy_colorForKey:kCK_Black_C] forState:UIControlStateNormal];
    [self.hotDiscussionButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];

    // 设置字体大小：选中17，未选中16
    self.searchHotButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];

    // 重新布局按钮（因为字体大小改变了）
    [self layoutTabButtonsWithAdaptiveWidth];

    // 移动指示器（自适应宽度）
    [UIView animateWithDuration:0.25 animations:^{
        [self updateIndicatorForButton:self.hotDiscussionButton];
    }];
}

// 迭代8.95：选中搜索热点tab（无动画）
- (void)selectSearchHotTabWithoutAnimation {
    // 设置颜色
    [self.searchHotButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];
    [self.hotDiscussionButton setTitleColor:[UIColor imy_colorForKey:kCK_Black_C] forState:UIControlStateNormal];

    // 设置字体大小：选中17，未选中16
    self.searchHotButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    self.hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];

    // 重新布局按钮（因为字体大小改变了）
    [self layoutTabButtonsWithAdaptiveWidth];

    // 直接设置指示器位置（无动画，自适应宽度）
    [self updateIndicatorForButton:self.searchHotButton];
}

// 迭代8.95：选中热门讨论tab（无动画）
- (void)selectHotDiscussionTabWithoutAnimation {
    // 设置颜色
    [self.searchHotButton setTitleColor:[UIColor imy_colorForKey:kCK_Black_C] forState:UIControlStateNormal];
    [self.hotDiscussionButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];

    // 设置字体大小：选中17，未选中16
    self.searchHotButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];

    // 重新布局按钮（因为字体大小改变了）
    [self layoutTabButtonsWithAdaptiveWidth];

    // 直接设置指示器位置（无动画，自适应宽度）
    [self updateIndicatorForButton:self.hotDiscussionButton];
}

#pragma mark - Public Methods

- (void)setupWithHotspotsKeyModels:(NSArray<IMYNASearchHotspotsKeyModel *> *)keyModels
                          animated:(BOOL)animated {
    [self.hotspotsView setupWithHotspotsKeyModels:keyModels animated:animated];
}

- (void)setupWithHotDiscussionKeyModels:(NSArray<IMYNASearchHotDiscussionKeyModel *> *)keyModels
                               animated:(BOOL)animated {
    [self.hotDiscussionView setupWithHotDiscussionKeyModels:keyModels animated:animated];
}

- (NSArray<IMYNASearchHotspotsKeyModel *> *)hotspotsKeys {
    return self.hotspotsView.hotspotsKeys;
}

- (NSArray<IMYNASearchHotDiscussionKeyModel *> *)hotDiscussionKeys {
    return self.hotDiscussionView.hotDiscussionKeys;
}

// 迭代8.95：动态控制tab显示状态
- (void)updateTabVisibilityWithHotDiscussionAvailable:(BOOL)hasHotDiscussion animated:(BOOL)animated {
    // 只有在实验组中才需要动态控制tab显示
    if (self.searchHotExperimentValue != 1 && self.searchHotExperimentValue != 2) {
        // 非实验组，始终显示单一热门搜索，无需处理
        return;
    }

    // 判断当前是否已经显示tab
    BOOL currentlyShowingTabs = (self.pageViewController != nil && self.pageViewController.view.superview != nil);

    if (hasHotDiscussion && !currentlyShowingTabs) {
        // 需要显示tab，但当前没有显示 - 切换到tab模式
        [self switchToTabModeAnimated:animated];
    } else if (!hasHotDiscussion && currentlyShowingTabs) {
        // 不需要显示tab，但当前正在显示 - 切换到单一模式
        [self switchToSingleModeAnimated:animated];
    }
}

#pragma mark - Private Methods

// 迭代8.95：切换到tab模式
- (void)switchToTabModeAnimated:(BOOL)animated {
    if (self.pageViewController && self.pageViewController.view.superview) {
        // 已经是tab模式，无需切换
        return;
    }

    // 移除单一模式的视图
    [self.hotspotsView removeFromSuperview];

    // 重新创建tab相关UI（不触发默认选中动画）
    [self recreateTabUIWithoutAnimation];

    // 添加页面控制器
    if (!self.pageViewController) {
        self.pageViewController = [[IMYPageViewController alloc] init];
        self.pageViewController.dataSource = self;
        self.pageViewController.delegate = self;
        self.pageViewController.cachePageCount = 2;
    }

    [self addSubview:self.pageViewController.view];

    // 迭代8.95：根据实验值设置正确的初始选中状态（无动画）
    [self setInitialTabSelectionWithoutAnimation];

    // 更新布局
    [self updateContainerHeight];

    if (animated) {
        self.pageViewController.view.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            self.pageViewController.view.alpha = 1;
        }];
    }
}

// 迭代8.95：切换到单一模式
- (void)switchToSingleModeAnimated:(BOOL)animated {
    if (!self.pageViewController || !self.pageViewController.view.superview) {
        // 已经是单一模式，无需切换
        return;
    }

    // 移除页面控制器
    [self.pageViewController.view removeFromSuperview];

    // 重新创建单一标题UI
    [self recreateSingleTitleUI];

    // 直接添加搜索热点视图
    [self addSubview:self.hotspotsView];
    self.hotspotsView.frame = CGRectMake(0, self.topBar.imy_bottom, self.imy_width, self.hotspotsView.imy_height);

    // 更新布局
    [self updateContainerHeight];

    if (animated) {
        self.hotspotsView.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            self.hotspotsView.alpha = 1;
        }];
    }
}

// 迭代8.95：重新创建tab UI（不触发默认选中动画）
- (void)recreateTabUIWithoutAnimation {
    // 清空topBar
    for (UIView *subview in self.topBar.subviews) {
        [subview removeFromSuperview];
    }

    // 重新创建tab容器（不触发默认选中）
    [self setupTabContainerWithoutDefaultSelection];
}

// 迭代8.95：重新创建tab UI（保留原有逻辑，用于其他场景）
- (void)recreateTabUI {
    // 清空topBar
    for (UIView *subview in self.topBar.subviews) {
        [subview removeFromSuperview];
    }

    // 重新创建tab容器
    [self setupTabContainer];
}

// 迭代8.95：重新创建单一标题UI
- (void)recreateSingleTitleUI {
    // 清空topBar
    for (UIView *subview in self.topBar.subviews) {
        [subview removeFromSuperview];
    }

    // 重新创建单一标题
    [self setupSingleTitle];
    self.titleLabel.text = self.name;
}

// 迭代8.95：创建tab容器（不触发默认选中）
- (void)setupTabContainerWithoutDefaultSelection {
    self.tabContainer = [[UIView alloc] initWithFrame:CGRectMake(12, 0, self.imy_width - 24, 44)];
    [self.topBar addSubview:self.tabContainer];

    // 创建并布局tab按钮（自适应宽度）
    [self createAndLayoutTabButtons];

    // 创建指示器
    [self createIndicator];

    // 不设置默认选中状态，等待后续手动设置
}

// 迭代8.95：创建并布局tab按钮（自适应宽度）
- (void)createAndLayoutTabButtons {
    // 创建搜索热点按钮
    self.searchHotButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.searchHotButton setTitle:@"搜索热点" forState:UIControlStateNormal];
    self.searchHotButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [self.searchHotButton addTarget:self action:@selector(onSearchHotButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.tabContainer addSubview:self.searchHotButton];

    // 创建热门讨论按钮
    self.hotDiscussionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.hotDiscussionButton setTitle:@"热门讨论" forState:UIControlStateNormal];
    self.hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [self.hotDiscussionButton addTarget:self action:@selector(onHotDiscussionButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.tabContainer addSubview:self.hotDiscussionButton];

    // 计算按钮宽度并布局
    [self layoutTabButtonsWithAdaptiveWidth];
}

// 迭代8.95：创建指示器
- (void)createIndicator {
    // 创建指示器 - 高度3，距离下方8，宽度根据按钮宽度调整
    self.indicatorView = [[UIView alloc] initWithFrame:CGRectMake(0, 44 - 3, 20, 3)];
    [self.indicatorView imy_setBackgroundColorForKey:kCK_Red_A];
    [self.indicatorView imy_drawAllCornerRadius:1.5];
    [self.tabContainer addSubview:self.indicatorView];
}

// 迭代8.95：自适应宽度布局tab按钮
- (void)layoutTabButtonsWithAdaptiveWidth {
    // 根据当前字体计算文本宽度
    CGFloat searchHotWidth = [self calculateButtonWidthForTitle:@"搜索热点" font:self.searchHotButton.titleLabel.font];
    CGFloat hotDiscussionWidth = [self calculateButtonWidthForTitle:@"热门讨论" font:self.hotDiscussionButton.titleLabel.font];

    // 设置按钮间距
    CGFloat buttonSpacing = 24;

    // 布局搜索热点按钮
    self.searchHotButton.frame = CGRectMake(0, 0, searchHotWidth, 44);

    // 布局热门讨论按钮
    self.hotDiscussionButton.frame = CGRectMake(self.searchHotButton.imy_right + buttonSpacing, 0, hotDiscussionWidth, 44);
}

// 迭代8.95：计算按钮宽度
- (CGFloat)calculateButtonWidthForTitle:(NSString *)title font:(UIFont *)font {
    // 计算文本宽度
    CGSize textSize = [title sizeWithAttributes:@{NSFontAttributeName: font}];

    // 添加左右内边距（每边8px）
    CGFloat padding = 16;

    // 设置最小宽度
    CGFloat minWidth = 60;

    return MAX(textSize.width + padding, minWidth);
}

// 迭代8.95：更新指示器位置和宽度
- (void)updateIndicatorForButton:(UIButton *)button {
    self.indicatorView.imy_centerX = button.imy_centerX;
}

// 迭代8.95：设置初始tab选中状态（无动画）
- (void)setInitialTabSelectionWithoutAnimation {
    if (self.searchHotExperimentValue == 2) {
        [self selectHotDiscussionTabWithoutAnimation];
        // 设置页面控制器到对应页面
        if (self.pageViewController) {
            [self.pageViewController setViewControllerAtIndex:1 animated:NO];
        }
    } else {
        [self selectSearchHotTabWithoutAnimation];
        // 设置页面控制器到对应页面
        if (self.pageViewController) {
            [self.pageViewController setViewControllerAtIndex:0 animated:NO];
        }
    }
}

- (void)updateContainerHeight {
    CGFloat maxHeight = MAX(self.hotspotsView.imy_height, self.hotDiscussionView.imy_height);
    CGFloat newHeight;

    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        // 有tab时
        newHeight = self.topBar.imy_bottom + maxHeight;
        self.pageViewController.view.frame = CGRectMake(0, self.topBar.imy_bottom + 8, self.imy_width, maxHeight);
    } else {
        // 无tab时，直接显示搜索热点
        newHeight = self.topBar.imy_bottom + self.hotspotsView.imy_height;
    }

    if (self.imy_height != newHeight) {
        self.imy_height = newHeight;

        if (self.onHeightDidChangedBlock) {
            self.onHeightDidChangedBlock(NO);
        }
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self updateContainerHeight];
}

#pragma mark - IMYPageViewControllerDataSource

- (NSUInteger)numberOfControllersInPageViewController:(IMYPageViewController *)pageViewController {
    // 根据实验值决定页面数量
    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        return 2; // 有tab时显示两个页面
    } else {
        return 1; // 无tab时只显示搜索热点
    }
}

- (UIViewController *)pageViewController:(IMYPageViewController *)pageViewController controllerAtIndex:(NSUInteger)index {
    UIViewController *containerVC = [[UIViewController alloc] init];
    containerVC.view.backgroundColor = [UIColor clearColor];

    if (index == 0) {
        // 搜索热点页面
        // 确保视图没有被添加到其他父视图
        [self.hotspotsView removeFromSuperview];
        [containerVC.view addSubview:self.hotspotsView];
        self.hotspotsView.frame = CGRectMake(0, 0, self.imy_width, self.hotspotsView.imy_height);
    } else if (index == 1) {
        // 热门讨论页面
        // 确保视图没有被添加到其他父视图
        [self.hotDiscussionView removeFromSuperview];
        [containerVC.view addSubview:self.hotDiscussionView];
        self.hotDiscussionView.frame = CGRectMake(0, 0, self.imy_width, self.hotDiscussionView.imy_height);
    }

    return containerVC;
}

#pragma mark - IMYPageViewControllerDelegate

- (void)pageViewController:(IMYPageViewController *)pageViewController didTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    // 同步tab状态
    if (toIndex == 0) {
        [self selectSearchHotTab];
    } else if (toIndex == 1) {
        [self selectHotDiscussionTab];
    }
}

- (void)pageViewController:(IMYPageViewController *)pageViewController willTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    // 收起键盘
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
}

@end
