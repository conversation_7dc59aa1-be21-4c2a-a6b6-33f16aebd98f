# Tab标题自适应宽度实现说明

## 实现概述

将热门讨论tab的标题从固定宽度（80px）改为根据文本内容自适应宽度，提供更好的视觉效果和布局灵活性。

## 核心改进

### 1. 模块化重构

**原始实现**:
- 在 `setupTabContainer` 和 `setupTabContainerWithoutDefaultSelection` 中直接创建按钮
- 硬编码按钮宽度为80px
- 固定按钮间距为20px或24px

**优化后实现**:
- 提取 `createAndLayoutTabButtons` 方法统一处理按钮创建
- 提取 `createIndicator` 方法统一处理指示器创建
- 提取 `layoutTabButtonsWithAdaptiveWidth` 方法处理自适应布局

### 2. 自适应宽度计算

**核心方法**:
```objc
// 计算按钮宽度
- (CGFloat)calculateButtonWidthForTitle:(NSString *)title font:(UIFont *)font;

// 自适应布局
- (void)layoutTabButtonsWithAdaptiveWidth;
```

**计算逻辑**:
1. 使用 `sizeWithAttributes:` 计算文本实际宽度
2. 添加左右内边距（总共16px）
3. 设置最小宽度（60px）确保按钮不会太小
4. 返回 `MAX(文本宽度 + 内边距, 最小宽度)`

### 3. 指示器自适应

**原始实现**:
- 固定指示器宽度为20px
- 简单居中对齐

**优化后实现**:
```objc
// 更新指示器位置和宽度
- (void)updateIndicatorForButton:(UIButton *)button;
```

**计算逻辑**:
- 指示器宽度 = 按钮宽度 × 70%
- 限制范围：最小20px，最大40px
- 居中对齐到按钮中心

### 4. 动态重布局

**关键优化**:
- 在选中状态改变时重新布局按钮
- 因为选中（17pt）和未选中（16pt）字体大小不同
- 确保布局始终准确

**实现方式**:
```objc
// 在所有选中方法中添加重布局
[self layoutTabButtonsWithAdaptiveWidth];
```

## 详细实现

### 1. 按钮创建和初始布局

```objc
- (void)createAndLayoutTabButtons {
    // 创建按钮（不设置frame）
    _searchHotButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_searchHotButton setTitle:@"搜索热点" forState:UIControlStateNormal];
    _searchHotButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    
    _hotDiscussionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_hotDiscussionButton setTitle:@"热门讨论" forState:UIControlStateNormal];
    _hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    
    // 自适应布局
    [self layoutTabButtonsWithAdaptiveWidth];
}
```

### 2. 宽度计算

```objc
- (CGFloat)calculateButtonWidthForTitle:(NSString *)title font:(UIFont *)font {
    // 计算文本宽度
    CGSize textSize = [title sizeWithAttributes:@{NSFontAttributeName: font}];
    
    // 添加内边距和最小宽度限制
    CGFloat padding = 16;  // 左右各8px
    CGFloat minWidth = 60;
    
    return MAX(textSize.width + padding, minWidth);
}
```

### 3. 布局逻辑

```objc
- (void)layoutTabButtonsWithAdaptiveWidth {
    // 根据当前字体计算宽度
    CGFloat searchHotWidth = [self calculateButtonWidthForTitle:@"搜索热点" 
                                                           font:_searchHotButton.titleLabel.font];
    CGFloat hotDiscussionWidth = [self calculateButtonWidthForTitle:@"热门讨论" 
                                                               font:_hotDiscussionButton.titleLabel.font];
    
    // 设置按钮位置
    _searchHotButton.frame = CGRectMake(0, 0, searchHotWidth, 44);
    _hotDiscussionButton.frame = CGRectMake(_searchHotButton.imy_right + 24, 0, hotDiscussionWidth, 44);
}
```

### 4. 指示器更新

```objc
- (void)updateIndicatorForButton:(UIButton *)button {
    // 自适应指示器宽度
    CGFloat indicatorWidth = button.imy_width * 0.7;
    indicatorWidth = MAX(20, MIN(indicatorWidth, 40));
    
    // 更新位置和大小
    self.indicatorView.imy_width = indicatorWidth;
    self.indicatorView.imy_centerX = button.imy_centerX;
}
```

## 兼容性保证

### 1. 向后兼容
- 保留原有的方法签名
- 不影响外部调用接口
- 保持原有的动画效果

### 2. 布局稳定性
- 设置最小宽度避免按钮过小
- 限制指示器宽度范围
- 保持合理的按钮间距

### 3. 性能优化
- 只在必要时重新计算布局
- 缓存字体对象避免重复创建
- 使用高效的文本宽度计算方法

## 预期效果

### 1. 视觉效果
- 按钮宽度完美贴合文本内容
- 指示器宽度与按钮宽度成比例
- 整体布局更加协调美观

### 2. 适应性
- 支持不同长度的标题文本
- 自动适应字体大小变化
- 响应选中状态的字体变化

### 3. 用户体验
- 点击区域更加精确
- 视觉层次更加清晰
- 动画效果更加流畅

## 测试建议

1. **文本长度测试**: 测试不同长度的标题文本
2. **字体大小测试**: 验证选中/未选中状态的字体变化
3. **动画测试**: 确认切换动画的流畅性
4. **边界测试**: 测试极短和极长的文本情况
5. **设备适配**: 在不同屏幕尺寸设备上测试布局效果
