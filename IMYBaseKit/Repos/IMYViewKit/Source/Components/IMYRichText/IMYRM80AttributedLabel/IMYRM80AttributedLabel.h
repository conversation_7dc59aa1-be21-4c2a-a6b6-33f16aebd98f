//
//  M80AttributedLabel.h
//  M80AttributedLabel
//
//  Created by amao on 13-9-1.
//  Copyright (c) 2013年 Netease. All rights reserved.
//

#import "IMYRM80AttributedLabelAttachment.h"
#import "IMYRM80AttributedLabelDefines.h"
#import "NSMutableAttributedString+IMYRM80.h"
#import <CoreText/CoreText.h>
#import <UIKit/UIKit.h>

@class IMYRM80AttributedLabelURL;

@interface IMYRM80AttributedLabel : UIView

@property (nonatomic, weak) id<IMYRM80AttributedLabelDelegate> delegate; /**< 点击事件回调*/
/**
 外部的点击手势会导致touch时间失效，走到touchesCancelled方法，使用这个标记位处理这种情况，正常处理点击高亮内容
 */
@property (nonatomic, assign) BOOL shouldCancleContinueTriggereClick;

@property (nonatomic, strong) UIFont *font; //字体
@property (nonatomic, strong) UIColor *textColor; //文字颜色
@property (nonatomic, strong) UIColor *highlightColor; //链接点击时背景高亮色
@property (nonatomic, strong) UIColor *linkColor; //链接色
@property (nonatomic, assign) BOOL underLineForLink; //链接是否带下划线
@property (nonatomic, assign) BOOL autoDetectLinks; //自动检测
@property (nonatomic, assign) NSInteger numberOfLines; //行数
@property (nonatomic, assign) CTTextAlignment textAlignment; //文字排版样式
@property (nonatomic, assign) CTLineBreakMode lineBreakMode; //LineBreakMode
@property (nonatomic, assign) CGFloat lineSpacing; //行间距
@property (nonatomic, assign) CGFloat minLineHeight; //最小行高
@property (nonatomic, assign) CGFloat paragraphSpacing; //段间距
@property (nonatomic, assign) CGFloat wordSpacing; //字间距
@property (nonatomic, assign) BOOL drawCenter; __deprecated_msg("drawCenter deprecated. Use Property singleLineCenter"); /**< 单行 上下间隙一致*/
@property (nonatomic, assign) BOOL singleLineCenter; /**< 单行 上下间隙一致*/
//是否启用长按操作 默认NO
@property (nonatomic, assign) BOOL shouldLongPress;
@property (nonatomic, assign) BOOL newStyle;

/// 开启<em></em>标签解析
@property (nonatomic, assign) BOOL enableEMTag;

/*
 不足以显示时的截断处理
 截断显示文字（默认是…）
 截断对应点击事件名称（因为有代理所以不用block了，判断linkData是否一样即可）
 */
@property (nonatomic, copy) NSAttributedString *truncationAttributedString;
@property (nonatomic, strong) id truncationLinkData;

/// 强制用BreakModeWord排版
@property (nonatomic, assign) BOOL forceLineBreakModeWord;

//普通文本
- (void)setText:(NSString *)text;
- (void)appendText:(NSString *)text;

//属性文本
- (void)setAttributedText:(NSAttributedString *)attributedText;
- (void)appendAttributedText:(NSAttributedString *)attributedText;

//UI控件 或者 图片 采用懒加载Block模式
- (void)appendUIBlock:(IMYRM80AttchmentBlockContent *)content;

- (void)appendUIBlock:(IMYRM80AttchmentBlockContent *)content
               margin:(UIEdgeInsets)margin;

- (void)appendUIBlock:(IMYRM80AttchmentBlockContent *)content
               margin:(UIEdgeInsets)margin
            alignment:(IMYRM80ImageAlignment)alignment;
;
/// 插入UI控件或者图片，适用于不改变文本的情况
- (void)insertUIBlock:(IMYRM80AttchmentBlockContent *)content margin:(UIEdgeInsets)margin
            alignment:(IMYRM80ImageAlignment)alignment atIndex:(NSUInteger)index;


//添加自定义链接 linkData 可以是任意一个对象
- (IMYRM80AttributedLabelURL *)addCustomLink:(id)linkData
                                    forRange:(NSRange)range;

- (IMYRM80AttributedLabelURL *)addCustomLink:(id)linkData
                                    forRange:(NSRange)range
                                   linkColor:(UIColor *)color;

//大小
- (CGSize)sizeThatFits:(CGSize)size;
- (CGSize)sizeThatFits:(CGSize)size numberOfLines:(NSInteger)numberOfLines;

//显示的行数
- (NSInteger)getLinesCount;

//设置全局的自定义Link检测Block(详见M80AttributedLabelURL)
+ (void)setCustomDetectMethod:(imyr_M80CustomDetectLinkBlock)block;

///取消长按的效果
- (void)resetLongPressedStatus;
@property BOOL isTouchPressedInside;

///拼接完的attributedString
- (NSAttributedString *)getBuildAttributedString;

///显示的attributedString
@property (strong, nonatomic) NSAttributedString *showAttributedString;
@property (strong, nonatomic) NSMutableArray *attachments;
@property (strong, nonatomic) NSMutableArray *linkLocations;
///原始赋值的string
@property (strong, nonatomic) NSString *originalString;

@property (nonatomic, assign) BOOL samelineSpacing; //每行同等行间距（不包括动态表情）

// MARK: - 子类使用，子类重写

@property (nonatomic, assign, readonly) CTFrameRef textFrame;

/**
 绘制其他的内容，子类重写该方法处理扩展绘制功能呢
 */
- (void)drawExtraContentInRect:(CGRect)rect context:(CGContextRef)context;

/// 类属性，当前处于截图渲染
@property (class, nonatomic, assign) BOOL isGlobalSnapshotDrawing;

/// 开启全局渲染缓存
@property (class, nonatomic, assign) BOOL enableGlobalSnapshotCaching;

/// 单组件开启截图缓存，只有首页的M80需要启用
@property (nonatomic, assign) BOOL enableSnapshotCache;

@end
