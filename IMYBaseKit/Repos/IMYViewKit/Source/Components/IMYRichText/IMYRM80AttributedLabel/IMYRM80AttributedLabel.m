//
//  M80AttributedLabel.m
//  M80AttributedLabel
//
//  Created by amao on 13-9-1.
//  Copyright (c) 2013年 Netease. All rights reserved.
//

#import "IMYRM80AttributedLabel.h"
#import "IMYRM80AttributedLabelAttachment.h"
#import "IMYRM80AttributedLabelURL.h"
#import "IMYViewKit.h"

static NSString *const kEllipsesCharacter = @"\u2026";

@interface IMYRM80AttributedLabel () <UIGestureRecognizerDelegate> {
    CTFrameRef _textFrame;
    CGFloat _fontAscent;
    CGFloat _fontDescent;
    CGFloat _fontHeight;

    NSInteger _linesCount;
    BOOL _isShowAllText;

    BOOL hasLongPressed;
}
///计算中的attributedString
@property (nonatomic, strong) NSMutableAttributedString *attributedString;
@property (nonatomic, strong) IMYRM80AttributedLabelURL *touchedLink;
@property (nonatomic, strong) NSMutableArray *touchedViewFrameArray;

@property (nonatomic, assign) BOOL linkDetected;

@property (nonatomic, strong) UILongPressGestureRecognizer *longPressGestureRecognizer;
@property (nonatomic, strong) NSRecursiveLock *recursiveLock;

@property (nonatomic, assign) CGRect willSetFrame;
@property (nonatomic, assign) CGRect willSetBounds;

@property (nonatomic, assign) CGFloat minlineSpacing; //每行同等行间距（不包括动态表情）
@property (nonatomic, strong) IMYRM80AttributedLabelURL *truncationURL; //截断对应的url，因为每次都会新增，所以记录下

@property (nonatomic, strong) UIImage *holdSnapshotCache;

@end

@implementation IMYRM80AttributedLabel

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        [self commonInit];
    }
    return self;
}

- (id)initWithCoder:(NSCoder *)aDecoder {
    self = [super initWithCoder:aDecoder];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (void)dealloc {
    if (_textFrame) {
        CFRelease(_textFrame);
        _textFrame = NULL;
    }
}

// MARK: - Public

- (CTFrameRef)textFrame {
    return _textFrame;
}

#pragma mark - 初始化
- (void)commonInit {
    _shouldLongPress = NO;
    _attributedString = [[NSMutableAttributedString alloc] init];
    _attachments = [[NSMutableArray alloc] init];
    _linkLocations = [[NSMutableArray alloc] init];
    _textFrame = nil;
    _linkColor = [UIColor colorWithRed:255 / 255.0 green:80 / 255.0 blue:115 / 255.0 alpha:1];
    _font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    _textColor = [UIColor blackColor];
    _highlightColor = [UIColor colorWithWhite:0.73f alpha:0.4f];

    if (IMYLanguageManager.supportMultiLanguages) {
        _lineBreakMode = kCTLineBreakByWordWrapping;
    } else {
        _lineBreakMode = kCTLineBreakByCharWrapping;
    }
    _textAlignment = kCTTextAlignmentLeft;
    _underLineForLink = NO;
    _autoDetectLinks = NO;
    _lineSpacing = 1.0;
    _paragraphSpacing = 0.0;

    _recursiveLock = [[NSRecursiveLock alloc] init];
    _touchedViewFrameArray = [[NSMutableArray array] init];

    self.userInteractionEnabled = YES;
    [self resetFont];
}

- (void)setAttachments:(NSMutableArray *)attachments {
    ///浅复制一份
    _attachments = [NSMutableArray arrayWithArray:attachments];
}

- (void)setLinkLocations:(NSMutableArray *)linkLocations {
    _linkLocations = [NSMutableArray arrayWithArray:linkLocations];
}

- (void)setShouldLongPress:(BOOL)shouldLongPress {
    _shouldLongPress = shouldLongPress;
    if (shouldLongPress) {
        if (self.longPressGestureRecognizer == nil) {
            UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPress:)];
            [self addGestureRecognizer:longPress];
            self.longPressGestureRecognizer = longPress;
        }
    } else {
        if (self.longPressGestureRecognizer) {
            [self removeGestureRecognizer:self.longPressGestureRecognizer];
            self.longPressGestureRecognizer = nil;
        }
    }
}
- (void)longPress:(UILongPressGestureRecognizer *)longPressGestureRecognizer {
    if (_shouldLongPress) {
        if (longPressGestureRecognizer.state == UIGestureRecognizerStateBegan) {
            hasLongPressed = YES;

            if (self.touchedLink) {
                if (_delegate && [_delegate respondsToSelector:@selector(m80AttributedLabel:longedOnLink:)]) {
                    [_delegate m80AttributedLabel:self longedOnLink:self.touchedLink];
                }
            }
        } else if (longPressGestureRecognizer.state >= UIGestureRecognizerStateEnded) {
            if (self.touchedLink) {
                self.touchedLink = nil;
                [self setNeedsDisplay];
            }
        }
    }
}

- (void)cleanAll {
    _linkDetected = NO;
    _attachments = [NSMutableArray array];
    _linkLocations = [NSMutableArray array];

    self.touchedLink = nil;
    
    if (NSThread.isMainThread) {
        NSArray *subviews = self.subviews;
        for (UIView *subView in subviews) {
            [subView removeFromSuperview];
        }
    } else {
        // 如果是在异步计算，则等到下个主线程进行移除，并标记需要重新绘制
        imy_asyncMainBlock(^{
            NSArray *subviews = self.subviews;
            if (subviews.count > 0) {
                for (UIView *subView in subviews) {
                    [subView removeFromSuperview];
                }
                [self setNeedsDisplay];
            }
        });
    }
    
    [self resetTextFrame];
}


- (void)resetTextFrame {
    if (_textFrame) {
        CFRelease(_textFrame);
        _textFrame = NULL;
    }
    imy_asyncMainExecuteBlock(^{
        // 支持 autolayout
        [self invalidateIntrinsicContentSize];
        // 需要重新渲染
        [self setNeedsDisplay];
    });
}

- (void)resetFont {
    CTFontRef fontRef = (__bridge CTFontRef)self.font;
    if (fontRef) {
        _fontAscent = CTFontGetAscent(fontRef);
        _fontDescent = CTFontGetDescent(fontRef);
        _fontHeight = CTFontGetSize(fontRef);
    }
}

#pragma mark - 属性设置

/// 保证正常绘制，如果传入nil就直接不处理
- (void)setFont:(UIFont *)font {
    if (!font) {
        return;
    }
    [_recursiveLock lock];
    if (_font != font) {
        _font = font;
        [_attributedString setFont:_font];
        [self resetFont];
        for (IMYRM80AttributedLabelAttachment *attachment in _attachments) {
            attachment.fontAscent = _fontAscent;
            attachment.fontDescent = _fontDescent;
        }
        [self resetTextFrame];
    }
    [_recursiveLock unlock];
}

/// 保证正常绘制，如果传入nil就直接不处理
- (void)setTextColor:(UIColor *)textColor {
    if (!textColor) {
        return;
    }
    [_recursiveLock lock];
    if (_textColor != textColor) {
        _textColor = textColor;
        [_attributedString setTextColor:textColor];
        [self resetDrawAttributedStringColors];
        [self resetTextFrame];
    }
    [_recursiveLock unlock];
}

- (void)setHighlightColor:(UIColor *)highlightColor {
    if (!highlightColor) {
        return;
    }
    if (_highlightColor != highlightColor) {
        _highlightColor = highlightColor;
        [self resetTextFrame];
    }
}

- (void)setLinkColor:(UIColor *)linkColor {
    if (!linkColor) {
        return;
    }
    [_recursiveLock lock];
    if (_linkColor != linkColor) {
        _linkColor = linkColor;
        [self resetDrawAttributedStringColors];
        [self resetTextFrame];
    }
    [_recursiveLock lock];
}

- (void)setFrame:(CGRect)frame {
    ///保证在异步给frame 赋值的时候   取frame是正常的
    _willSetFrame = frame;
    NSString *queueKey = [NSString stringWithFormat:@"M80_setFrame_%p", self];
    if ([NSThread isMainThread]) {
        [NSObject imy_cancelBlockForKey:queueKey];
        
        CGRect oldRect = [super frame];
        BOOL areAnimationsEnabled = [UIView areAnimationsEnabled];
        if (areAnimationsEnabled) {
            [UIView setAnimationsEnabled:NO];
        }
        [super setFrame:frame];
        if (areAnimationsEnabled) {
            [UIView setAnimationsEnabled:YES];
        }
        
        if (!CGSizeEqualToSize(oldRect.size, frame.size)) {
            [self resetTextFrame];
        }
    } else {
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [self setFrame:frame];
        } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:queueKey];
    }
}

- (CGRect)frame {
    if ([NSThread isMainThread]) {
        return [super frame];
    } else {
        return _willSetFrame;
    }
}

- (void)setBounds:(CGRect)bounds {
    _willSetBounds = bounds;
    NSString *queueKey = [NSString stringWithFormat:@"M80_setBounds_%p", self];
    if ([NSThread isMainThread]) {
        [NSObject imy_cancelBlockForKey:queueKey];
        
        CGRect oldRect = [super bounds];
        BOOL areAnimationsEnabled = [UIView areAnimationsEnabled];
        if (areAnimationsEnabled) {
            [UIView setAnimationsEnabled:NO];
        }
        [super setBounds:bounds];
        if (areAnimationsEnabled) {
            [UIView setAnimationsEnabled:YES];
        }
        
        if (!CGRectEqualToRect(bounds, oldRect)) {
            [self resetTextFrame];
        }
    } else {
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [self setBounds:bounds];
        } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:queueKey];
    }
}

- (CGRect)bounds {
    if ([NSThread isMainThread]) {
        return [super bounds];
    } else {
        return _willSetBounds;
    }
}

- (void)setNeedsDisplay {
    if ([NSThread isMainThread]) {
        [super setNeedsDisplay];
    }
}

#pragma mark - 辅助方法
- (NSAttributedString *)attributedString:(NSString *)text {
    if ([text length]) {
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:text];
        [string setFont:self.font];
        [string setTextColor:self.textColor];
        return string;
    } else {
        return [[NSAttributedString alloc] init];
    }
}

- (NSInteger)numberOfDisplayedLines {
    CFArrayRef lines = CTFrameGetLines(_textFrame);
    return _numberOfLines > 0 ? MIN(CFArrayGetCount(lines), _numberOfLines) : CFArrayGetCount(lines);
}

- (NSAttributedString *)showAttributedString {
    if (_showAttributedString == nil) {
        ///加锁
        [_recursiveLock lock];

        if (_attributedString.length > 0) {
            if (_showAttributedString == nil) {
                //添加排版格式
                NSMutableAttributedString *drawString = [_attributedString mutableCopy];

                //如果LineBreakMode为TranncateTail,那么默认排版模式改成kCTLineBreakByCharWrapping,使得尽可能地显示所有文字
                CTLineBreakMode lineBreakMode = self.lineBreakMode;
                if (self.lineBreakMode == kCTLineBreakByTruncatingTail) {
                    lineBreakMode = _numberOfLines >= 1 ? kCTLineBreakByCharWrapping : kCTLineBreakByWordWrapping;
                    if (self.forceLineBreakModeWord) {
                        lineBreakMode = kCTLineBreakByWordWrapping;
                    }
                }

                CGFloat fontLineHeight = self.font.lineHeight;
                fontLineHeight = MAX(fontLineHeight, self.minLineHeight);
                if (self.samelineSpacing) {
                    fontLineHeight = MAX(self.minlineSpacing, fontLineHeight);
                }
                
                CTTextAlignment textAlignment = _textAlignment;
                if (IMYISRTL && !self.imy_disableAutoRTL) {
                    if (kCTTextAlignmentLeft == textAlignment || kCTTextAlignmentNatural == textAlignment) {
                        textAlignment = kCTTextAlignmentRight;
                    }
                } else {
                    if (kCTTextAlignmentNatural == textAlignment) {
                        textAlignment = kCTTextAlignmentLeft;
                    }
                }
                CTParagraphStyleSetting settings[] = {
                    {kCTParagraphStyleSpecifierAlignment, sizeof(textAlignment), &textAlignment},
                    {kCTParagraphStyleSpecifierLineBreakMode, sizeof(lineBreakMode), &lineBreakMode},
                    {kCTParagraphStyleSpecifierMaximumLineSpacing, sizeof(_lineSpacing), &_lineSpacing},
                    {kCTParagraphStyleSpecifierMinimumLineSpacing, sizeof(_lineSpacing), &_lineSpacing},
                    {kCTParagraphStyleSpecifierParagraphSpacing, sizeof(_paragraphSpacing), &_paragraphSpacing},
                    {kCTParagraphStyleSpecifierMinimumLineHeight, sizeof(fontLineHeight), &fontLineHeight},
                };
                CTParagraphStyleRef paragraphStyle = CTParagraphStyleCreate(settings, sizeof(settings) / sizeof(settings[0]));
                [drawString addAttribute:(id)kCTParagraphStyleAttributeName
                                   value:(__bridge id)paragraphStyle
                                   range:NSMakeRange(0, [drawString length])];
                CFRelease(paragraphStyle);

                if (_wordSpacing) {
                    [drawString addAttribute:(id)kCTKernAttributeName
                                       value:@(_wordSpacing)
                                       range:NSMakeRange(0, [drawString length])];
                }
                
                for (int i = 0; i < _linkLocations.count;) {
                    IMYRM80AttributedLabelURL *url = _linkLocations[i];
                    ///移除不符合条件的url
                    if ([url isKindOfClass:[IMYRM80AttributedLabelURL class]] && (url.range.location + url.range.length <= [_attributedString length])) {
                        UIColor *drawLinkColor = url.color ?: self.linkColor;
                        [drawString setTextColor:drawLinkColor range:url.range];
                        CTUnderlineStyle underlineStyle = _underLineForLink ? kCTUnderlineStyleSingle : kCTUnderlineStyleNone;
                        if (url.alinkObject.isEMkeyword) {
                            underlineStyle = kCTUnderlineStyleNone;
                        }
                        [drawString setUnderlineStyle:underlineStyle
                                             modifier:kCTUnderlinePatternSolid
                                                range:url.range];
                        /// 给每段link添加属性，避免url是连续的，且UI属性相同时，在获取CTLineGetGlyphRuns时，会被当成整体来识别
                        [drawString addAttribute:@"IMY_LinkURL_Index" value:@(i) range:url.range];
                        i++;
                    } else {
                        [_linkLocations removeObjectAtIndex:i];
                    }
                }
                _showAttributedString = drawString;
            }
        } else {
            _showAttributedString = nil;
        }
        ///解锁
        [_recursiveLock unlock];
    }
    return _showAttributedString;
}

- (void)resetDrawAttributedStringColors {
    // 重新生成渲染的 AttributedString 对应色值
    NSMutableAttributedString * const drawString = _showAttributedString;
    if (!drawString) {
        return;
    }
    const NSUInteger length = [drawString length];
    [drawString setTextColor:_textColor range:NSMakeRange(0, length)];
    for (IMYRM80AttributedLabelURL *url in _linkLocations) {
        if (![url isKindOfClass:IMYRM80AttributedLabelURL.class]) {
            continue;
        }
        if (url.range.location + url.range.length <= length) {
            UIColor *drawLinkColor = url.color ?: _linkColor;
            [drawString setTextColor:drawLinkColor range:url.range];
        }
    }
}

- (NSAttributedString *)attributedStringForDraw {
    if (!_showAttributedString) {
        [self showAttributedString];
    }
    return _showAttributedString;
}

- (void)fillURLFrames {
    if (_textFrame == nil || _linkLocations.count == 0) {
        return;
    }
    CFArrayRef lines = CTFrameGetLines(_textFrame);
    if (!lines) {
        return;
    }

    CFIndex lineCount = CFArrayGetCount(lines);
    IMYRM80AttributedLabelURL *firstCheck = _linkLocations[0];
    if (firstCheck.displayLineCount == lineCount) {
        //行数限制的 已有缓存
        return;
    }

    if (_isShowAllText) {
        if (firstCheck.showFrames.count > 0) {
            //全文显示 也有缓存了
            return;
        }
    } else {
        for (IMYRM80AttributedLabelURL *url in _linkLocations) {
            [url.displayFrames removeAllObjects];
        }
    }

    CGPoint origins[lineCount];
    CTFrameGetLineOrigins(_textFrame, CFRangeMake(0, 0), origins);

    NSInteger lineTextIndex = 0;
    for (int i = 0; i < lineCount; i++) {
        CGPoint linePoint = origins[i];

        CTLineRef line = CFArrayGetValueAtIndex(lines, i);
        //        NSInteger lineTextCount = CTLineGetGlyphCount(line);
        CFRange lineRange = CTLineGetStringRange(line);
        NSInteger lineTextCount = lineRange.length;

        for (IMYRM80AttributedLabelURL *url in _linkLocations) {
            BOOL isLineContainURL = NO;
            if (lineTextIndex <= url.range.location && url.range.location <= (lineTextIndex + lineTextCount)) {
                isLineContainURL = YES;
            } else if (lineTextIndex > url.range.location && url.range.location + url.range.length > lineTextIndex) {
                isLineContainURL = YES;
            }
            if (isLineContainURL) {
                CGRect highlightRect = [self rectForRange:url.range
                                                   inLine:line
                                               lineOrigin:linePoint];
                if (CGRectIsEmpty(highlightRect) == NO) {
                    highlightRect.origin.y = self.frame.size.height - highlightRect.origin.y - highlightRect.size.height;

                    highlightRect.origin.y -= 1;
                    highlightRect.size.height += 1;

                    if (_isShowAllText) {
                        [url.showFrames addObject:[NSValue valueWithCGRect:highlightRect]];
                    } else {
                        url.displayLineCount = lineCount;
                        [url.displayFrames addObject:[NSValue valueWithCGRect:highlightRect]];
                    }
                }
            }
        }

        lineTextIndex += lineTextCount;
    }
}
- (IMYRM80AttributedLabelURL *)urlForPoint:(CGPoint)point {
    for (IMYRM80AttributedLabelURL *url in _linkLocations) {
        if (url.alinkObject.isEMkeyword) {
            continue;
        }
        NSMutableArray *showFrames = nil;
        if (_isShowAllText) {
            showFrames = url.showFrames;
        } else {
            showFrames = url.displayFrames;
        }

        for (NSValue *frameValue in showFrames) {

            CGRect frame = frameValue.CGRectValue;
            frame.origin.y -= 2;
            frame.origin.x -= 2;
            frame.size.width += 4;
            frame.size.height += 4;
            
            if ([url.linkData isEqual:self.truncationLinkData]) {
                /// 尾部的需要增加点击响应范围
                frame.origin.y -= 5;
                frame.size.height += 10;
            }
            if (CGRectContainsPoint(frame, point)) {
                url.frame = frame;
                return url;
            }
        }
    }
    return nil;
}

- (BOOL)viewFrameForPoint:(CGPoint)point {
    NSArray *touchedViewFrameArray = self.touchedViewFrameArray;
    for (NSValue *frameValue in touchedViewFrameArray) {
        CGRect frame = frameValue.CGRectValue;
        if (CGRectContainsPoint(frame, point)) {
            return YES;
        }
    }
    return NO;
}

- (id)linkDataForPoint:(CGPoint)point {
    IMYRM80AttributedLabelURL *url = [self urlForPoint:point];
    return url ? url.linkData : nil;
}

- (CGAffineTransform)transformForCoreText {
    return CGAffineTransformScale(CGAffineTransformMakeTranslation(0, self.bounds.size.height), 1.f, -1.f);
}

- (CGRect)getLineBounds:(CTLineRef)line point:(CGPoint)point {
    CGFloat ascent = 0.0f;
    CGFloat descent = 0.0f;
    CGFloat leading = 0.0f;
    CGFloat width = (CGFloat)CTLineGetTypographicBounds(line, &ascent, &descent, &leading);
    CGFloat height = ascent + descent;

    return CGRectMake(point.x, point.y - descent, width, height);
}

- (IMYRM80AttributedLabelURL *)linkAtIndex:(CFIndex)index {
    for (IMYRM80AttributedLabelURL *url in _linkLocations) {
        if (NSLocationInRange(index, url.range)) {
            return url;
        }
    }
    return nil;
}


- (CGRect)rectForRange:(NSRange)range
                inLine:(CTLineRef)line
            lineOrigin:(CGPoint)lineOrigin {
    CGRect rectForRange = CGRectZero;
    CFArrayRef runs = CTLineGetGlyphRuns(line);
    CFIndex runCount = CFArrayGetCount(runs);

    // Iterate through each of the "runs" (i.e. a chunk of text) and find the runs that
    // intersect with the range.
    for (CFIndex k = 0; k < runCount; k++) {
        CTRunRef run = CFArrayGetValueAtIndex(runs, k);

        CFRange stringRunRange = CTRunGetStringRange(run);
        NSRange lineRunRange = NSMakeRange(stringRunRange.location, stringRunRange.length);
        NSRange intersectedRunRange = NSIntersectionRange(lineRunRange, range);

        if (intersectedRunRange.length == 0) {
            // This run doesn't intersect the range, so skip it.
            continue;
        }

        CGFloat ascent = 0.0f;
        CGFloat descent = 0.0f;
        CGFloat leading = 0.0f;

        // Use of 'leading' doesn't properly highlight Japanese-character link.
        CGFloat width = (CGFloat)CTRunGetTypographicBounds(run,
                                                           CFRangeMake(0, 0),
                                                           &ascent,
                                                           &descent,
                                                           NULL); //&leading);
        CGFloat height = ascent + descent;

        CGFloat xOffset = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location, nil);
        CGFloat xOffsetLast = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location + CTRunGetStringRange(run).length, nil);
        /// 这里验证下书写方向，如果从右往左写的，frame的其实要扣掉width
        if (xOffsetLast < xOffset) {
            xOffset = xOffset - width;
        }
        CGRect linkRect = CGRectMake(lineOrigin.x + xOffset - leading, lineOrigin.y - descent, width + leading, height);

        linkRect.origin.y = roundf(linkRect.origin.y);
        linkRect.origin.x = roundf(linkRect.origin.x);
        linkRect.size.width = roundf(linkRect.size.width);
        linkRect.size.height = roundf(linkRect.size.height);

        rectForRange = CGRectIsEmpty(rectForRange) ? linkRect : CGRectUnion(rectForRange, linkRect);
    }

    return rectForRange;
}

- (void)appendAttachment:(IMYRM80AttributedLabelAttachment *)attachment atIndex:(NSUInteger)index {
    attachment.fontAscent = _fontAscent;
    attachment.fontDescent = _fontDescent;
    unichar objectReplacementChar = 0xFFFC;
    NSString *objectReplacementString = [NSString stringWithCharacters:&objectReplacementChar length:1];
    NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:objectReplacementString];

    CGSize size = [attachment.content m80ContentSize];
    self.minlineSpacing = MAX(self.minlineSpacing, size.height);

    CTRunDelegateCallbacks callbacks;
    callbacks.version = kCTRunDelegateVersion1;
    callbacks.getAscent = ascentCallback;
    callbacks.getDescent = descentCallback;
    callbacks.getWidth = widthCallback;
    callbacks.dealloc = deallocCallback;

    CTRunDelegateRef delegate = CTRunDelegateCreate(&callbacks, (__bridge_retained void *)attachment);
    NSDictionary *attr = [NSDictionary dictionaryWithObjectsAndKeys:(__bridge id)delegate, kCTRunDelegateAttributeName, nil];
    [attachText setAttributes:attr range:NSMakeRange(0, 1)];
    CFRelease(delegate);

    [_attachments addObject:attachment];
    
    [_recursiveLock lock];
    [_attributedString insertAttributedString:attachText atIndex:index];
    [self resetTextFrame];
    [_recursiveLock unlock];
}


#pragma mark - 设置文本
- (void)setText:(NSString *)text {
    NSAttributedString *attributedText = [self attributedString:text];
    [self setAttributedText:attributedText];
}

- (void)setAttributedText:(NSAttributedString *)attributedText {
    _linesCount = 0;
    _showAttributedString = nil;
    _attributedString = [[NSMutableAttributedString alloc] initWithAttributedString:attributedText];

    NSUInteger stringLength = _attributedString.length;
    if (stringLength > 0) {
        NSUInteger i = 0;
        NSRange range = NSMakeRange(0, 0);
        while (i < stringLength) {
            NSDictionary *dic = [_attributedString attributesAtIndex:i effectiveRange:&range];
            if (range.length == 0) {
                break;
            } else {
                NSArray *array = dic.allKeys;
                if ([array containsObject:@"CTRunDelegate"] == NO) {
                    if ([array containsObject:@"NSFont"] == NO) {
                        [_attributedString setFont:_font range:range];
                    }
                    if ([array containsObject:@"CTForegroundColor"] == NO) {
                        [_attributedString setTextColor:_textColor range:range];
                    }
                }

                i += range.length;
            }
        }
    }
    [self cleanAll];
}


#pragma mark - 添加文本
- (void)appendText:(NSString *)text {
    NSAttributedString *attributedText = [self attributedString:text];
    [self appendAttributedText:attributedText];
}

- (void)appendAttributedText:(NSAttributedString *)attributedText {
    [_recursiveLock lock];
    [_attributedString appendAttributedString:attributedText];
    [self resetTextFrame];
    [_recursiveLock unlock];
}

#pragma mark - 添加图片 和 添加UI控件
- (void)appendUIBlock:(IMYRM80AttchmentBlockContent *)content {
    [self appendUIBlock:content margin:UIEdgeInsetsZero];
}
- (void)appendUIBlock:(IMYRM80AttchmentBlockContent *)content margin:(UIEdgeInsets)margin {
    [self appendUIBlock:content margin:margin alignment:IMYRM80ImageAlignmentCenter];
}
- (void)appendUIBlock:(IMYRM80AttchmentBlockContent *)content margin:(UIEdgeInsets)margin alignment:(IMYRM80ImageAlignment)alignment {
    [self insertUIBlock:content margin:margin alignment:alignment atIndex:self.attributedString.length];
}



- (void)insertUIBlock:(IMYRM80AttchmentBlockContent *)content margin:(UIEdgeInsets)margin
            alignment:(IMYRM80ImageAlignment)alignment atIndex:(NSUInteger)index {
    IMYRM80AttributedLabelAttachment *attachment = [IMYRM80AttributedLabelAttachment attachmentWith:content
                                                                                             margin:margin
                                                                                          alignment:alignment
                                                                                            maxSize:CGSizeZero];
    [self appendAttachment:attachment atIndex:index];
}

#pragma mark - 添加链接
- (IMYRM80AttributedLabelURL *)addCustomLink:(id)linkData
                                    forRange:(NSRange)range {
    return [self addCustomLink:linkData
                      forRange:range
                     linkColor:nil];
}

- (IMYRM80AttributedLabelURL *)addCustomLink:(id)linkData
                                    forRange:(NSRange)range
                                   linkColor:(UIColor *)color {
    IMYRM80AttributedLabelURL *url = [IMYRM80AttributedLabelURL urlWithLinkData:linkData
                                                                          range:range
                                                                          color:color];
    [_linkLocations addObject:url];
    [self resetTextFrame];
    return url;
}

#pragma mark - 计算大小

- (CGSize)sizeThatFits:(CGSize const)size {
    return [self sizeThatFits:size numberOfLines:_numberOfLines];
}

- (CGSize)sizeThatFits:(CGSize const)size numberOfLines:(NSInteger const)numberOfLines {
    NSAttributedString *drawString = [self attributedStringForDraw];
    if (drawString == nil) {
        return CGSizeZero;
    }
    CFAttributedStringRef attributedStringRef = (__bridge CFAttributedStringRef)drawString;
    CTFramesetterRef framesetter = CTFramesetterCreateWithAttributedString(attributedStringRef);
    CFRange range = CFRangeMake(0, 0);
    if (numberOfLines > 0 && framesetter) {
        CGMutablePathRef path = CGPathCreateMutable();
        CGPathAddRect(path, NULL, CGRectMake(0, 0, size.width, size.height));
        CTFrameRef frame = CTFramesetterCreateFrame(framesetter, CFRangeMake(0, 0), path, NULL);
        CFArrayRef lines = CTFrameGetLines(frame);

        if (nil != lines && CFArrayGetCount(lines) > 0) {
            NSInteger lastVisibleLineIndex = MIN(numberOfLines, CFArrayGetCount(lines)) - 1;
            CTLineRef lastVisibleLine = CFArrayGetValueAtIndex(lines, lastVisibleLineIndex);

            CFRange rangeToLayout = CTLineGetStringRange(lastVisibleLine);
            range = CFRangeMake(0, rangeToLayout.location + rangeToLayout.length);
        }
        CFRelease(frame);
        CFRelease(path);
    }

    CFRange fitCFRange = CFRangeMake(0, 0);
    CGSize newSize = CTFramesetterSuggestFrameSizeWithConstraints(framesetter, range, NULL, size, &fitCFRange);
    if (framesetter) {
        CFRelease(framesetter);
    }
    return CGSizeMake(ceil(newSize.width), ceil(newSize.height));

    //hack:
    //1.需要加上额外的一部分size,有些情况下计算出来的像素点并不是那么精准
    //2.ios7的CTFramesetterSuggestFrameSizeWithConstraints方法比较残,需要多加一部分height
    //3.ios7多行中如果首行带有很多空格，会导致返回的suggestionWidth远小于真是width,那么多行情况下就是用传入的width
    //    if (IMYRM80IOS7)
    //    {
    //        if (newSize.height < _fontHeight * 2)   //单行
    //        {
    //            return CGSizeMake(ceilf(newSize.width) + 2.0, ceilf(newSize.height) + 4.0);
    //        }
    //        else
    //        {
    //            return CGSizeMake(size.width, ceilf(newSize.height) + 4.0);
    //        }
    //    }
    //    else
    //    {
    //        return CGSizeMake(ceilf(newSize.width) + 2.0, ceilf(newSize.height) + 2.0);
    //    }
}
- (NSInteger)getLinesCount {
    NSAttributedString *drawString = [self attributedStringForDraw];
    if (drawString == nil) {
        _linesCount = 0;
    } else if (_linesCount == 0) {
        CFAttributedStringRef attributedStringRef = (__bridge CFAttributedStringRef)drawString;
        CTFramesetterRef framesetter = CTFramesetterCreateWithAttributedString(attributedStringRef);
        if (framesetter) {
            CGMutablePathRef path = CGPathCreateMutable();
            CGPathAddRect(path, NULL, CGRectMake(0, 0, self.frame.size.width, CGFLOAT_MAX));
            CTFrameRef frame = CTFramesetterCreateFrame(framesetter, CFRangeMake(0, 0), path, NULL);
            CFArrayRef lines = CTFrameGetLines(frame);

            if (nil != lines) {
                _linesCount = CFArrayGetCount(lines);
            }
            CFRelease(frame);
            CFRelease(path);
            CFRelease(framesetter);
        }
    }
    return _linesCount;
}

- (CGSize)intrinsicContentSize {
    return [self sizeThatFits:CGSizeMake(CGRectGetWidth(self.bounds), CGFLOAT_MAX)];
}

#pragma mark - 绘制方法
- (void)drawRect:(CGRect)rect {
    // 使用缓存绘制
    if (IMYRM80AttributedLabel.isGlobalSnapshotDrawing && self.enableSnapshotCache) {
        [self.holdSnapshotCache drawInRect:self.bounds];
        return;
    }
    
    [self.touchedViewFrameArray removeAllObjects];

    CGContextRef ctx = UIGraphicsGetCurrentContext();
    if (ctx == nil) {
        return;
    }
    CGContextSaveGState(ctx);
    CGAffineTransform transform = [self transformForCoreText];
    CGContextConcatCTM(ctx, transform);

    [self recomputeLinksIfNeeded];

    NSAttributedString *drawString = [self attributedStringForDraw];
    if (drawString) {
        [self prepareTextFrame:drawString rect:rect];
        [self drawHighlightWithRect:rect];
        [self drawAttachments];
        [self drawText:drawString
                  rect:rect
               context:ctx];
    }
    [self drawExtraContentInRect:rect context:ctx];
    CGContextRestoreGState(ctx);
    
    // 开启绘制缓存
    if (self.enableSnapshotCache) {
        CGImageRef imageRef = CGBitmapContextCreateImage(ctx);
        if (imageRef) {
            self.holdSnapshotCache = [UIImage imageWithCGImage:imageRef];
            CGImageRelease(imageRef);
        }
    }
}

/**
 绘制其他的内容，子类重写该方法处理扩展绘制功能呢
 */
- (void)drawExtraContentInRect:(CGRect)rect context:(CGContextRef)context {
    
}

- (void)prepareTextFrame:(NSAttributedString *)string
                    rect:(CGRect)rect {
    if (_textFrame == nil) {
        CTFramesetterRef framesetter = CTFramesetterCreateWithAttributedString((__bridge CFAttributedStringRef)string);
        CGMutablePathRef path = CGPathCreateMutable();
        CGPathAddRect(path, nil, rect);
        CTFrameRef frameRef = CTFramesetterCreateFrame(framesetter, CFRangeMake(0, 0), path, NULL);
        _textFrame = CFRetain(frameRef);
        
        // 填充富文本内容
        if (_textFrame) {
            CFArrayRef lines = CTFrameGetLines(_textFrame);
            if (lines) {
                CFIndex lineCount = CFArrayGetCount(lines);
                _isShowAllText = (self.getLinesCount == lineCount);
            }
        }
        [self fillURLFrames];
        
        // 释放临时对象
        CFRelease(frameRef);
        CGPathRelease(path);
        CFRelease(framesetter);
    }
}

- (void)drawHighlightWithRect:(CGRect)rect {
    if (self.touchedLink && self.highlightColor && !self.touchedLink.hideHighlightColor) {
        [self.highlightColor setFill];

        CGContextRef ctx = UIGraphicsGetCurrentContext();

        NSArray *showFrames = nil;
        if (_isShowAllText) {
            showFrames = self.touchedLink.showFrames;
        } else {
            showFrames = self.touchedLink.displayFrames;
        }

        for (NSValue *frameValue in showFrames) {
            CGRect highlightRect = frameValue.CGRectValue;
            ///绘制的时候 坐标是倒着的 so...
            highlightRect.origin.y = rect.size.height - highlightRect.origin.y - highlightRect.size.height;
            highlightRect = CGRectOffset(highlightRect, 0, -rect.origin.y);

            CGFloat pi = (CGFloat)M_PI;

            CGFloat radius = 1.0f;
            CGContextMoveToPoint(ctx, highlightRect.origin.x, highlightRect.origin.y + radius);
            CGContextAddLineToPoint(ctx, highlightRect.origin.x, highlightRect.origin.y + highlightRect.size.height - radius);
            CGContextAddArc(ctx, highlightRect.origin.x + radius, highlightRect.origin.y + highlightRect.size.height - radius,
                            radius, pi, pi / 2.0f, 1.0f);
            CGContextAddLineToPoint(ctx, highlightRect.origin.x + highlightRect.size.width - radius,
                                    highlightRect.origin.y + highlightRect.size.height);
            CGContextAddArc(ctx, highlightRect.origin.x + highlightRect.size.width - radius,
                            highlightRect.origin.y + highlightRect.size.height - radius, radius, pi / 2, 0.0f, 1.0f);
            CGContextAddLineToPoint(ctx, highlightRect.origin.x + highlightRect.size.width, highlightRect.origin.y + radius);
            CGContextAddArc(ctx, highlightRect.origin.x + highlightRect.size.width - radius, highlightRect.origin.y + radius,
                            radius, 0.0f, -pi / 2.0f, 1.0f);
            CGContextAddLineToPoint(ctx, highlightRect.origin.x + radius, highlightRect.origin.y);
            CGContextAddArc(ctx, highlightRect.origin.x + radius, highlightRect.origin.y + radius, radius,
                            -pi / 2, pi, 1);
            CGContextFillPath(ctx);
        }
    }
}

- (void)drawText:(NSAttributedString *)attributedString
            rect:(CGRect)rect
         context:(CGContextRef)context {
    if (self.truncationURL) {
        [_linkLocations removeObject:self.truncationURL];
        self.truncationURL = nil;
    }
    if (_textFrame) {
        if (_numberOfLines > 0) {
            CFArrayRef lines = CTFrameGetLines(_textFrame);
            NSInteger numberOfLines = [self numberOfDisplayedLines];

            CGPoint lineOrigins[numberOfLines];
            CTFrameGetLineOrigins(_textFrame, CFRangeMake(0, numberOfLines), lineOrigins);

            for (CFIndex lineIndex = 0; lineIndex < numberOfLines; lineIndex++) {
                CGPoint lineOrigin = lineOrigins[lineIndex];
                if (self.drawCenter) {
                    CGContextSetTextPosition(context, lineOrigin.x, (self.frame.size.height - self.font.lineHeight) / 2 + 2);
                } else {
                    CGContextSetTextPosition(context, lineOrigin.x, lineOrigin.y);
                }


                CTLineRef line = CFArrayGetValueAtIndex(lines, lineIndex);

                BOOL shouldDrawLine = YES;
                if (lineIndex == _numberOfLines - 1 &&
                    _lineBreakMode == kCTLineBreakByTruncatingTail) {
                    // Does the last line need truncation?
                    CFRange lastLineRange = CTLineGetStringRange(line);
                    if (lastLineRange.location + lastLineRange.length < attributedString.length) {
                        CTLineTruncationType truncationType = kCTLineTruncationEnd;

                        NSAttributedString *tokenString = self.truncationAttributedString;
                        if (!tokenString) {
                            NSUInteger truncationAttributePosition = lastLineRange.location + lastLineRange.length - 1;

                            NSMutableDictionary *tokenAttributes = [attributedString attributesAtIndex:truncationAttributePosition
                                                                                        effectiveRange:NULL]
                                                                       .mutableCopy;
                            tokenString = [[NSAttributedString alloc] initWithString:kEllipsesCharacter
                                                                          attributes:tokenAttributes];
                        }

                        CTLineRef truncationToken = CTLineCreateWithAttributedString((__bridge CFAttributedStringRef)tokenString);
                        double truncationTokenWidth = CTLineGetTypographicBounds(truncationToken, NULL, NULL, NULL);

                        NSMutableAttributedString *truncationString = [[attributedString attributedSubstringFromRange:NSMakeRange(lastLineRange.location, lastLineRange.length)] mutableCopy];
                        if ([truncationString.string hasSuffix:@"\r\n"]) {
                            /// 结尾是换行的，要去掉
                            [truncationString replaceCharactersInRange:[truncationString.string rangeOfString:@"\r\n"] withString:@""];
                            lastLineRange.length = truncationString.length;
                        }
                        if (lastLineRange.length > 0) {
                            // Remove Characters to fit truncationTokenWidth
                            // 创建完整一行文本来算宽度，之前采用累加的方案 最终宽度不正确
                            NSMutableAttributedString *tmpTTString = [truncationString mutableCopy];
                            [tmpTTString appendAttributedString:tokenString];
                            CTLineRef tmpLine = CTLineCreateWithAttributedString((__bridge CFAttributedStringRef)tmpTTString);
                            double tmpLineWidth = CTLineGetTypographicBounds(tmpLine, NULL, NULL, NULL);
                            while (truncationString.length > 1 && ceil(tmpLineWidth) > rect.size.width) {
                                /// composedRange 保证emoji表情不被部分截断
                                NSRange composedRange = [truncationString.string rangeOfComposedCharacterSequencesForRange:NSMakeRange(truncationString.length - 1, 1)];
                                [truncationString deleteCharactersInRange:composedRange];
                                CFRelease(tmpLine);
                                tmpTTString = [truncationString mutableCopy];
                                [tmpTTString appendAttributedString:tokenString];
                                tmpLine = CTLineCreateWithAttributedString((__bridge CFAttributedStringRef)tmpTTString);
                                tmpLineWidth = CTLineGetTypographicBounds(tmpLine, NULL, NULL, NULL);
                            }
                            CFRelease(tmpLine);
                        }
                        [truncationString appendAttributedString:tokenString];
                        CTLineRef truncationLine = CTLineCreateWithAttributedString((__bridge CFAttributedStringRef)truncationString);
                        double truncationLineWidth = CTLineGetTypographicBounds(truncationLine, NULL, NULL, NULL);
                        if (self.textAlignment == kCTTextAlignmentRight) {
                            CGPoint textPosition = CGContextGetTextPosition(context);
                            /// 右对齐的，绘制点需要再往左偏一下，调整
                            CGContextSetTextPosition(context, self.imy_width - truncationLineWidth, textPosition.y);
                        }
                        // iOS问题修复：创建的TruncatedLine宽度参数不能小于truncationLineWidth
                        CTLineRef truncatedLine = CTLineCreateTruncatedLine(truncationLine, MAX(rect.size.width, truncationLineWidth), truncationType, truncationToken);
                        
                        if (kCTTextAlignmentJustified == _textAlignment && truncatedLine) {
                            double truncatedLineWidth = CTLineGetTypographicBounds(truncationLine, NULL, NULL, NULL);
                            // iOS问题修复：创建的JustifiedLine宽度参数不能小于truncatedLineWidth
                            CTLineRef justifiedLine = CTLineCreateJustifiedLine(truncatedLine, 1.0, MAX(rect.size.width, truncatedLineWidth));
                            if (justifiedLine) {
                                CFRelease(truncatedLine);
                                truncatedLine = justifiedLine;
                            }
                        }

                        if (!truncatedLine) {
                            // If the line is not as wide as the truncationToken, truncatedLine is NULL
                            truncatedLine = CFRetain(truncationToken);
                        }

                        //加入点击事件
                        if (self.truncationLinkData) {
                            IMYRM80AttributedLabelURL *url = [IMYRM80AttributedLabelURL urlWithLinkData:self.truncationLinkData
                                                                                                  range:NSMakeRange(truncationString.string.length - tokenString.string.length, tokenString.string.length)
                                                                                                  color:nil];
                            url.hideHighlightColor = YES;

                            CFIndex lineCount = CFArrayGetCount(lines);
                            CGPoint origins[lineCount];
                            CTFrameGetLineOrigins(_textFrame, CFRangeMake(0, 0), origins);
                            CGPoint linePoint = origins[lineIndex];
                            CFRange lineRange = CTLineGetStringRange(truncatedLine);

                            CGRect highlightRect = [self rectForRange:url.range
                                                               inLine:truncatedLine
                                                           lineOrigin:linePoint];
                            if (CGRectIsEmpty(highlightRect) == NO) {
                                highlightRect.origin.y = self.frame.size.height - highlightRect.origin.y - highlightRect.size.height;

                                highlightRect.origin.y -= 1;
                                highlightRect.size.height += 1;

                                if (_isShowAllText) {
                                    [url.showFrames addObject:[NSValue valueWithCGRect:highlightRect]];
                                } else {
                                    url.displayLineCount = lineCount;
                                    [url.displayFrames addObject:[NSValue valueWithCGRect:highlightRect]];
                                }
                                self.truncationURL = url;
                                [_linkLocations insertObject:self.truncationURL atIndex:0];
                            }
                        }

                        CFRelease(truncationLine);
                        CFRelease(truncationToken);

                        if (self.singleLineCenter && lineIndex == 0 && numberOfLines == 1) {
                            CGFloat ascent = 0.0f, descent = 0.0f, leading = 0.0f;
                            CTLineGetTypographicBounds((CTLineRef)truncatedLine, &ascent, &descent, &leading);
                            CGFloat startY = (self.frame.size.height - (ascent + descent)) / 2;
                            CGContextSetTextPosition(context, lineOrigin.x, lineOrigin.y-startY);
                        }
                        
                        CTLineDraw(truncatedLine, context);
                        CFRelease(truncatedLine);


                        shouldDrawLine = NO;
                    }
                }
                if (shouldDrawLine) {
                    if (self.singleLineCenter && lineIndex == 0 && numberOfLines == 1) {
                        CGFloat ascent = 0.0f, descent = 0.0f, leading = 0.0f;
                        CTLineGetTypographicBounds((CTLineRef)line, &ascent, &descent, &leading);
                        CGFloat startY = (self.frame.size.height - (ascent + descent)) / 2;
                        CGContextSetTextPosition(context, lineOrigin.x, lineOrigin.y-startY);
                    }
                    CTLineDraw(line, context);
                }
            }
        } else {
            if (self.singleLineCenter) {
                NSInteger numberOfLines = [self numberOfDisplayedLines];
                if (numberOfLines == 1) {
                    CFArrayRef lines = CTFrameGetLines(_textFrame);
                    CGPoint lineOrigins[numberOfLines];
                    CTFrameGetLineOrigins(_textFrame, CFRangeMake(0, numberOfLines), lineOrigins);
                    CGPoint lineOrigin = lineOrigins[0];
                    CTLineRef line = CFArrayGetValueAtIndex(lines, 0);
                    CGFloat ascent = 0.0f, descent = 0.0f, leading = 0.0f;
                    CTLineGetTypographicBounds((CTLineRef)line, &ascent, &descent, &leading);
                    CGFloat startY = (self.frame.size.height - (ascent + descent)) / 2;
                    CGContextSetTextPosition(context, lineOrigin.x, lineOrigin.y-startY);
                    CTLineDraw(line, context);
                } else{
                    CTFrameDraw(_textFrame, context);
                }
            } else
            CTFrameDraw(_textFrame, context);
        }
    }
}


- (void)drawAttachments {
    if ([_attachments count] == 0) {
        return;
    }
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    if (ctx == nil) {
        return;
    }
    CFArrayRef lines = CTFrameGetLines(_textFrame);
    CFIndex lineCount = CFArrayGetCount(lines);
    NSInteger numberOfLines = (_numberOfLines > 0 ? MIN(lineCount, _numberOfLines) : lineCount);

    NSAttributedString *drawString = [self attributedStringForDraw];
    
    IMYRM80AttributedLabelAttachment *firstCheckFrame = _attachments[0];
    ///有行数限定
    if (firstCheckFrame.displayLineCount == numberOfLines) {
        for (IMYRM80AttributedLabelAttachment *attachment in _attachments) {
            ///在显示的行数内
            if (attachment.displayLine < numberOfLines) {
                [self drawAttachments:attachment frame:attachment.displayFrame context:ctx];
            }
        }
        return;
    }
    ///显示全文
    if (numberOfLines == self.getLinesCount) {
        if (CGRectIsEmpty(firstCheckFrame.allFrame) == NO) {
            for (IMYRM80AttributedLabelAttachment *attachment in _attachments) {
                [self drawAttachments:attachment frame:attachment.allFrame context:ctx];
            }
            return;
        }
    }

    ///没缓存就去计算
    CGPoint lineOrigins[lineCount];
    CTFrameGetLineOrigins(_textFrame, CFRangeMake(0, 0), lineOrigins);
    for (CFIndex i = 0; i < numberOfLines; i++) {
        CTLineRef line = CFArrayGetValueAtIndex(lines, i);
        CFArrayRef runs = CTLineGetGlyphRuns(line);
        CFIndex runCount = CFArrayGetCount(runs);
        CGPoint lineOrigin = lineOrigins[i];
        CGFloat lineAscent;
        CGFloat lineDescent;
        CTLineGetTypographicBounds(line, &lineAscent, &lineDescent, NULL);
        CGFloat lineHeight = lineAscent + lineDescent;
        CGFloat lineBottomY = lineOrigin.y - lineDescent;

        // Iterate through each of the "runs" (i.e. a chunk of text) and find the runs that
        // intersect with the range.
        for (CFIndex k = 0; k < runCount; k++) {
            CTRunRef run = CFArrayGetValueAtIndex(runs, k);
            NSDictionary *runAttributes = (NSDictionary *)CTRunGetAttributes(run);
            CTRunDelegateRef delegate = (__bridge CTRunDelegateRef)[runAttributes valueForKey:(id)kCTRunDelegateAttributeName];
            if (nil == delegate) {
                continue;
            }
            IMYRM80AttributedLabelAttachment *attributedImage = (IMYRM80AttributedLabelAttachment *)CTRunDelegateGetRefCon(delegate);
            if (attributedImage == nil || [attributedImage isKindOfClass:[IMYRM80AttributedLabelAttachment class]] == NO) {
                continue;
            }
            CGFloat width = [attributedImage boxSize].width;
            CGFloat imageBoxHeight = [attributedImage boxSize].height;
            CGFloat xOffset = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location, nil);
            CGFloat xOffsetLast = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location + CTRunGetStringRange(run).length, nil);
            /// 这里验证下书写方向，如果从右往左写的，frame的其实要扣掉width
            if (xOffsetLast < xOffset) {
                xOffset = xOffset - width;
            }

            CGFloat imageBoxOriginY = 0.0f;
            switch (attributedImage.alignment) {
                case IMYRM80ImageAlignmentTop:
                    imageBoxOriginY = lineBottomY + (lineHeight - imageBoxHeight);
                    break;
                case IMYRM80ImageAlignmentCenter:
                    imageBoxOriginY = lineBottomY + (lineHeight - imageBoxHeight) / 2.0;
                    if (self.singleLineCenter && i == 0 && numberOfLines == 1) {
                        imageBoxOriginY = (self.frame.size.height - imageBoxHeight) / 2.0;
                    }
                    break;
                case IMYRM80ImageAlignmentBottom:
                    imageBoxOriginY = lineBottomY;
                    break;
            }

            CGRect rect = CGRectMake(lineOrigin.x + xOffset, imageBoxOriginY, width, imageBoxHeight);
            UIEdgeInsets flippedMargins = attributedImage.margin;
            CGFloat top = flippedMargins.top;
            flippedMargins.top = flippedMargins.bottom;
            flippedMargins.bottom = top;

            CGRect attatchmentRect = UIEdgeInsetsInsetRect(rect, flippedMargins);

            if (i == _numberOfLines - 1 &&
                k >= runCount - 3 &&
                _lineBreakMode == kCTLineBreakByTruncatingTail) {
                
                CGFloat truncateWidth = 22;
                NSAttributedString *tokenString = self.truncationAttributedString;
                BOOL needTruncate = NO;
                CFRange lastLineRange = CTLineGetStringRange(line);
                if (lastLineRange.location + lastLineRange.length > drawString.length) {
                    continue;
                }
                
                NSMutableAttributedString *truncationString = [[drawString attributedSubstringFromRange:NSMakeRange(lastLineRange.location, lastLineRange.length)] mutableCopy];
                /// leftDrawString 剩余未绘制的content
                NSAttributedString *leftDrawString = [drawString attributedSubstringFromRange:NSMakeRange(lastLineRange.location, drawString.length - lastLineRange.location)];
                if (![leftDrawString isEqualToAttributedString:truncationString]) {
                    /// 最后一行内容与未绘制的内容不相同时，需要展示尾巴
                    needTruncate = YES;
                }
                
                if (!tokenString) {
                    NSUInteger truncationAttributePosition = lastLineRange.location + lastLineRange.length - 1;
                    NSMutableDictionary *tokenAttributes = [drawString attributesAtIndex:truncationAttributePosition effectiveRange:NULL].mutableCopy;
                    tokenString = [[NSAttributedString alloc] initWithString:kEllipsesCharacter
                                                                  attributes:tokenAttributes];
                }

                CTLineRef tmpLine = CTLineCreateWithAttributedString((__bridge CFAttributedStringRef)tokenString);
                double tmpLineWidth = CTLineGetTypographicBounds(tmpLine, NULL, NULL, NULL);
                CFRelease(tmpLine);
                truncateWidth = tmpLineWidth;
                
                if (needTruncate) {
                    // 判断表情显示后是否超...省略号位置
                    if (attatchmentRect.origin.x + attatchmentRect.size.width > self.frame.size.width - truncateWidth) {
                        continue;
                    }
                } else {
                    // 无截取判断
                    if (attatchmentRect.origin.x + attatchmentRect.size.width > self.frame.size.width) {
                        continue;
                    }
                }
                //                CGFloat attachmentWidth = CGRectGetWidth(attatchmentRect);
                //                const CGFloat kMinEllipsesWidth = attachmentWidth;
                //                if (CGRectGetWidth(self.bounds) - CGRectGetMinX(attatchmentRect) - attachmentWidth <  kMinEllipsesWidth)
                //                {
                //                    continue;
                //                }
            }

            [self drawAttachments:attributedImage frame:attatchmentRect context:ctx];

            if (numberOfLines == self.getLinesCount) {
                attributedImage.allFrame = attatchmentRect;
                attributedImage.allLine = i;
            } else {
                attributedImage.displayLineCount = numberOfLines;
                attributedImage.displayFrame = attatchmentRect;
                attributedImage.displayLine = i;
            }
        }
    }
}
- (void)drawAttachments:(IMYRM80AttributedLabelAttachment *)attributedImage frame:(CGRect)attatchmentRect context:(CGContextRef)ctx {
    id content = attributedImage.content;

    if ([content isKindOfClass:[IMYRM80AttchmentBlockContent class]]) {
        IMYRM80AttchmentBlockContent *blockContent = content;
        if (blockContent.contentView) {
            content = blockContent.contentView;
        } else if (blockContent.viewBlock) {
            blockContent.contentView = blockContent.viewBlock(blockContent.size);
            blockContent.viewBlock = nil;
            content = blockContent.contentView;
        } else if (blockContent.contentImage) {
            content = blockContent.contentImage;
        } else if (blockContent.imageBlock) {
            blockContent.contentImage = blockContent.imageBlock();
            blockContent.imageBlock = nil;
            content = blockContent.contentImage;
        }
    }

    if ([content isKindOfClass:[UIImage class]]) {
        CGContextDrawImage(ctx, attatchmentRect, ((UIImage *)content).CGImage);
    } else if ([content isKindOfClass:[UIView class]]) {
        UIView *view = (UIView *)content;
        if (view.superview == nil || view.superview != self) {
            [self addSubview:view];
        }
        CGRect viewFrame = CGRectMake(attatchmentRect.origin.x,
                                      self.bounds.size.height - attatchmentRect.origin.y - attatchmentRect.size.height,
                                      attatchmentRect.size.width,
                                      attatchmentRect.size.height);

        if ([view respondsToSelector:@selector(m80CanBeTapped)] && [(id)view m80CanBeTapped]) {
            [self.touchedViewFrameArray addObject:[NSValue valueWithCGRect:viewFrame]];
        }

        [view setFrame:viewFrame];
    } else {
        NSLog(@"Attachment Content Not Supported %@", content);
    }
}


#pragma mark - 点击事件处理
#pragma mark - 链接处理
- (void)recomputeLinksIfNeeded {
    const NSInteger kMinHttpLinkLength = 5;
    if (!_autoDetectLinks || _linkDetected) {
        return;
    }
    NSString *text = [[_attributedString string] copy];
    NSUInteger length = [text length];
    if (length <= kMinHttpLinkLength) {
        return;
    }
    BOOL sync = length <= IMYRM80MinAsyncDetectLinkLength;
    [self computeLink:text
                 sync:sync];
}

- (void)computeLink:(NSString *)text
               sync:(BOOL)sync {
    __weak typeof(self) weakSelf = self;
    typedef void (^LinkBlock)(NSArray *);
    LinkBlock block = ^(NSArray *links) {
        weakSelf.linkDetected = YES;
        if ([links count]) {
            for (IMYRM80AttributedLabelURL *link in links) {
                [weakSelf addAutoDetectedLink:link];
            }
            [weakSelf resetTextFrame];
        }
    };

    if (sync) {
        NSArray *links = [IMYRM80AttributedLabelURL detectLinks:text];
        block(links);
    } else {
        dispatch_async([NSObject imy_queueWithLevel:IMYQueueLevelHighlight], ^{
            NSArray *links = [IMYRM80AttributedLabelURL detectLinks:text];
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *plainText = [[weakSelf attributedString] string];
                if ([plainText isEqualToString:text]) {
                    block(links);
                }
            });
        });
    }
}

- (void)addAutoDetectedLink:(IMYRM80AttributedLabelURL *)link {
    NSRange range = link.range;
    for (IMYRM80AttributedLabelURL *url in _linkLocations) {
        if (NSIntersectionRange(range, url.range).length != 0) {
            return;
        }
    }
    [self addCustomLink:link.linkData
               forRange:link.range];
}

#pragma mark - 点击事件相应
- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    id value = [self urlForPoint:point];
    BOOL pointInside = (value != nil);
    if (pointInside) {
        return pointInside;
    }
    pointInside = [self viewFrameForPoint:point];

    return pointInside;
}
//判断手势的作用对象

- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    UITouch *touch = [touches anyObject];
    CGPoint point = [touch locationInView:self];
    hasLongPressed = NO;
    _isTouchPressedInside = YES;

    self.touchedLink = [self urlForPoint:point];
    if (self.touchedLink) {
        [self setNeedsDisplay];
    }
}
- (void)touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event {
    if (hasLongPressed) {
        return;
    }

    UITouch *touch = [touches anyObject];
    CGPoint point = [touch locationInView:self];

    IMYRM80AttributedLabelURL *touchedLink = [self urlForPoint:point];
    if (self.touchedLink != touchedLink) {
        self.touchedLink = touchedLink;
        [self setNeedsDisplay];
    }
}

- (void)touchesCancelled:(NSSet *)touches withEvent:(UIEvent *)event {
    _isTouchPressedInside = NO;
    ///没触发长按手势 才 取消点击状态
    if (hasLongPressed) {
        return;
    }
    if (self.touchedLink) {
        if (self.shouldCancleContinueTriggereClick) {
            if (_delegate && [_delegate respondsToSelector:@selector(m80AttributedLabel:clickedOnLink:)]) {
                [_delegate m80AttributedLabel:self clickedOnLink:self.touchedLink];
            }
        }
        self.touchedLink = nil;
        [self setNeedsDisplay];
    }
    self.shouldCancleContinueTriggereClick = NO;
}

- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    _isTouchPressedInside = NO;
    ///没触发长按手势 才 取消点击状态
    if (hasLongPressed) {
        return;
    }

    if (self.touchedLink) {
        if (_delegate && [_delegate respondsToSelector:@selector(m80AttributedLabel:clickedOnLink:)]) {
            [_delegate m80AttributedLabel:self clickedOnLink:self.touchedLink];
        }
        self.touchedLink = nil;
        [self setNeedsDisplay];
    }
}
- (void)resetLongPressedStatus {
    hasLongPressed = NO;
    self.touchedLink = nil;
    [self setNeedsDisplay];
}

- (NSAttributedString *)getBuildAttributedString {
    return _attributedString;
}

- (void)didMoveToWindow {
    [super didMoveToWindow];
    
    if (!self.window) {
        return;
    }
    
    // 当前开启全局渲染缓存
    if (IMYRM80AttributedLabel.enableGlobalSnapshotCaching) {
        self.enableSnapshotCache = YES;
    }
}

#pragma mark - 类方法

+ (void)setCustomDetectMethod:(imyr_M80CustomDetectLinkBlock)block {
    [IMYRM80AttributedLabelURL setCustomDetectMethod:block];
}

static BOOL kGlobalSnapshotDrawing = NO;
+ (BOOL)isGlobalSnapshotDrawing {
    return kGlobalSnapshotDrawing;
}

+ (void)setIsGlobalSnapshotDrawing:(BOOL)isGlobalSnapshotDrawing {
    kGlobalSnapshotDrawing = isGlobalSnapshotDrawing;
}

static BOOL kEnableGlobalSnapshotCaching = NO;
+ (BOOL)enableGlobalSnapshotCaching {
    return kEnableGlobalSnapshotCaching;
}

+ (void)setEnableGlobalSnapshotCaching:(BOOL)enableGlobalSnapshotCaching {
    kEnableGlobalSnapshotCaching = enableGlobalSnapshotCaching;
}

@end
